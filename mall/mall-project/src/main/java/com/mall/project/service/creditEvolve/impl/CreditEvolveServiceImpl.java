package com.mall.project.service.creditEvolve.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.creditEvolve.CreditEvolveDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.creditEvolve.CreditEvolveService;
import com.mall.project.util.MallBAuthUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信用值进化量服务实现类
 */
@Service
@Slf4j
public class CreditEvolveServiceImpl implements CreditEvolveService {
    @Autowired
    private CreditEvolveDao creditEvolveDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    /**
     * 计算信用值进化量
     */
    @Override
    public void updateCreditEvolve() {
        creditEvolveDao.updateCreditEvolve();
    }

    @Override
    public CommonPage<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = creditEvolveDao.queryCreditEvolvePages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = creditEvolveDao.totalCreditEvolve(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        CreditEvolveServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new CreditEvolveServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalCreditEvolve", todayTotalCreditEvolve(phone,startDate));   //今日信用值进化量
        summary.put("totalCreditEvolve", totalCreditEvolve(phone,startDate));   //累计信用值进化量
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    @Override
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone, String startDate, String endDate) {
        if(startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if(endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return creditEvolveDao.exportCreditEvolveExcel(phone,startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    @Override
    public String todayTotalCreditEvolve(String phone,String startDate) {
        // 转换下划线格式为驼峰格式
        return creditEvolveDao.todayTotalCreditEvolve(phone,startDate) == null ? "0" : creditEvolveDao.todayTotalCreditEvolve(phone,startDate);
    }

    @Override
    public String totalCreditEvolve(String phone,String startDate) {
        // 转换下划线格式为驼峰格式
        return creditEvolveDao.totalCreditEvolve(phone,startDate) == null ? "0" : creditEvolveDao.totalCreditEvolve(phone,startDate);
    }

    /**
     * 去mallB系统读取用户类型为B的信用值进化量
     */
    @Override
    public void getCreditEvolveFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();
            ResponseEntity<String> creditEvolveResponse = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/faith", String.class);

            // 检查获取信用值进化量数据是否成功
            if (creditEvolveResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统信用值进化量数据失败: " + creditEvolveResponse.getStatusCode());
            }

            // 解析信用值进化量数据响应
            String creditEvolveResponseBody = creditEvolveResponse.getBody();
            if (creditEvolveResponseBody == null) {
                throw new BusinessException("获取mallB系统信用值进化量数据响应为空");
            }

            // 解析响应JSON
            JsonNode creditEvolveRoot = objectMapper.readTree(creditEvolveResponseBody);

            if (creditEvolveRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统信用值进化量数据失败: " + creditEvolveRoot.get("msg").asText());
            }

            // 获取信用值进化量数据列表
            JsonNode creditEvolveList = creditEvolveRoot.path("rows");
            if (!creditEvolveList.isArray() || creditEvolveList.isEmpty()) {
                log.info("今日mallB系统无信用值进化量数据");
                return;
            }
            // 存储信用值进化量数据到本地系统
            log.info("开始处理并存储mallB信用值进化量数据，共 {} 条记录", creditEvolveList.size());

            // 遍历信用值进化量数据并存储
            for (JsonNode evolveItem : creditEvolveList) {

                try {
                    String phone = evolveItem.path("phone").asText();
                    String creditEvolve = evolveItem.path("dayFaithAmount").asText();

                    // 调用DAO层存储数据
                    creditEvolveDao.saveMallBCreditEvolveData(phone, creditEvolve);

                } catch (Exception e) {
                    log.error("处理mallB信用值进化量数据项失败: {}", e.getMessage(), e);
                    return;
                }
            }
            log.info("成功完成mallB系统信用值进化量数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 去mallB 读取 Admin量化进化量  数据
     */
    @Override
    public void getAdminCreditEvolveFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();
            ResponseEntity<String> adminCreditResponse = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/adminFaith", String.class);

            // 检查获取Admin量化进化量数据是否成功
            if (adminCreditResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据失败: " + adminCreditResponse.getStatusCode());
            }

            // 解析Admin量化进化量数据响应
            String adminCreditResponseBody = adminCreditResponse.getBody();
            if (adminCreditResponseBody == null) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据响应为空");
            }

            // 解析响应JSON
            JsonNode adminCreditRoot = objectMapper.readTree(adminCreditResponseBody);

            if (adminCreditRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统Admin量化进化量数据失败: " + adminCreditRoot.get("msg").asText());
            }

            // 获取Admin量化进化量数据
            JsonNode dataNode = adminCreditRoot.path("data");
            if (dataNode.isMissingNode() || dataNode.isNull()) {
                log.info("今日mallB系统无Admin量化进化量数据");
                return;
            }

            // 从data节点中提取数据
            String phone = dataNode.path("phone").asText();
            double value = dataNode.path("dayFaithAmount").asDouble();
            String updateDate = dataNode.path("date").asText();

            //log.info("开始处理并存储mallB Admin量化进化量数据: phone={}, value={}, date={}", phone, value, updateDate);

            // 最后保存数据
            creditEvolveDao.saveOrUpdateAdminCreditEvolve(phone, String.valueOf(value));

            //log.info("成功完成mallB系统Admin量化进化量数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }
}
